@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-head: var(--font-head);
  --font-sans: var(--font-sans);

  --shadow-xs: 1px 1px 0 0 var(--border);
  --shadow-sm: 2px 2px 0 0 var(--border);
  --shadow: 3px 3px 0 0 var(--border);
  --shadow-md: 4px 4px 0 0 var(--border);
  --shadow-lg: 6px 6px 0 0 var(--border);
  --shadow-xl: 10px 10px 0 1px var(--border);
  --shadow-2xl: 16px 16px 0 1px var(--border);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-primary-hover: var(--primary-hover);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
}

:root {
  --background: #fff;
  --foreground: #000;
  --card: #fff;
  --card-foreground: #000;
  --primary: #ffdb33;
  --primary-hover: #ffcc00;
  --primary-foreground: #000;
  --secondary: #000;
  --secondary-foreground: #fff;
  --muted: #aeaeae;
  --muted-foreground: #5a5a5a;
  --accent: #fae583;
  --accent-foreground: #000;
  --destructive: #e63946;
  --destructive-foreground: #fff;
  --border: #000;
}

.dark {
  --background: #1a1a1a;
  --foreground: #f5f5f5;
  --card: #242424;
  --card-foreground: #f5f5f5;
  --primary: #ffdb33;
  --primary-hover: #ffcc00;
  --primary-foreground: #000;
  --secondary: #3a3a3a;
  --secondary-foreground: #f5f5f5;
  --muted: #3f3f46;
  --muted-foreground: #a0a0a0;
  --accent: #fae583;
  --accent-foreground: #000;
  --destructive: #e63946;
  --destructive-foreground: #fff;
  --border: #3a3a3a;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
