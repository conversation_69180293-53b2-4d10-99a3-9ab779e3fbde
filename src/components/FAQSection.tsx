import { Text } from "@/components/retroui/Text";
import { Badge } from "@/components/retroui/Badge";

export function FAQSection() {
  const faqs = [
    {
      question: "What is dexnity.com and how does it work?",
      answer:
        "dexnity.com is a comprehensive Solana DEX tools platform that provides everything you need to create, manage, and trade tokens on the Solana blockchain. We integrate with leading protocols like Raydium and OpenBook to offer professional-grade DeFi tools in a user-friendly interface.",
    },
    {
      question: "How do I create a token on Solana using dexnity.com?",
      answer:
        "Creating a token is simple with our intuitive token creation wizard. Connect your Solana wallet, specify your token details (name, symbol, supply, metadata), configure tokenomics settings, and deploy with just a few clicks. The entire process takes less than 5 minutes and costs minimal SOL for transaction fees.",
    },
    {
      question: "What wallets are supported?",
      answer:
        "We support all major Solana wallets including Phantom, Solflare, Backpack, Glow, and any wallet compatible with the Solana Wallet Adapter. Simply connect your preferred wallet to start using our platform.",
    },
    {
      question: "How does liquidity management work with Raydium?",
      answer:
        "Our platform provides direct integration with Raydium's AMM pools. You can create new liquidity pools, add/remove liquidity, track your LP positions, monitor yields, and access advanced features like concentrated liquidity and yield farming—all from our unified dashboard.",
    },
    {
      question: "What are the fees for using dexnity.com?",
      answer:
        "dexnity.com charges competitive fees: Token creation starts at 0.1 SOL, liquidity operations have a 0.25% fee, and advanced features like OpenBook integration have tiered pricing. All Solana network fees (gas) are additional and paid directly to the network.",
    },
    {
      question: "Is dexnity.com secure and audited?",
      answer:
        "Yes, security is our top priority. Our smart contracts have been audited by leading security firms, we maintain a bug bounty program, and all funds are protected by insurance coverage. We also implement best practices like multi-sig controls and regular security monitoring.",
    },
    {
      question: "Can I use dexnity.com for market making on OpenBook?",
      answer:
        "Absolutely! Our OpenBook integration provides professional market making tools including order book management, advanced order types, automated strategies, and API access for algorithmic trading. Perfect for both individual traders and institutional market makers.",
    },
    {
      question: "Do you offer API access for developers?",
      answer:
        "Yes, we provide comprehensive REST and WebSocket APIs for developers who want to integrate our services into their applications. Our API covers token operations, liquidity management, market data, and more. Documentation and SDKs are available for popular programming languages.",
    },
    {
      question: "What kind of support do you provide?",
      answer:
        "We offer multiple support channels: comprehensive documentation, video tutorials, community Discord server, and direct support for enterprise customers. Our team is available 24/7 to help with technical issues and platform questions.",
    },
    {
      question: "How do I get started with dexnity.com?",
      answer:
        "Getting started is easy! Simply connect your Solana wallet, explore our tools, and start with our free tier. No registration required—just connect and go. For advanced features, you can upgrade to our Pro or Enterprise plans as needed.",
    },
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4">FAQ</Badge>
          <Text as="h2" className="mb-6">
            Frequently Asked Questions
          </Text>
          <Text as="p" className="text-xl text-muted-foreground">
            Everything you need to know about dexnity.com and our Solana DeFi
            tools
          </Text>
        </div>

        {/* FAQ List */}
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="p-6 bg-background border-2 border-border shadow-sm"
            >
              <Text as="h5" className="font-semibold mb-3">
                {faq.question}
              </Text>
              <Text as="p" className="text-muted-foreground leading-relaxed">
                {faq.answer}
              </Text>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="mt-16 text-center p-8 bg-background border-2 border-border shadow-sm">
          <Text as="h4" className="mb-4">
            Still have questions?
          </Text>
          <Text as="p" className="text-muted-foreground mb-6">
            Our team is here to help. Reach out through our support channels or
            join our community.
          </Text>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground border-2 border-border shadow-sm hover:shadow-md transition-all duration-200 font-medium"
            >
              Contact Support
            </a>
            <a
              href="https://discord.gg/dexnity"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-6 py-3 bg-background text-foreground border-2 border-border shadow-sm hover:shadow-md transition-all duration-200 font-medium"
            >
              Join Discord
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
