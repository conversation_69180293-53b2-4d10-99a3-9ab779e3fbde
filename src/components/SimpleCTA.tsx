import { Button } from "@/components/retroui/Button";
import { Text } from "@/components/retroui/Text";

export function SimpleCTA() {
  return (
    <section className="py-20 bg-primary/5 border-t-2 border-border">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <Text as="h2" className="mb-6">
          Ready to Start Building on{" "}
          <Text as="span" variant="brand" className="text-accent-foreground">
            Solana
          </Text>
          ?
        </Text>

        <Text
          as="p"
          className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto"
        >
          Join thousands of developers and traders who trust dexnity.com for
          their Solana DeFi needs. Get started in minutes.
        </Text>

        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <Button size="lg">Launch App Now</Button>
          <Button variant="outline" size="lg">
            View Documentation
          </Button>
        </div>

        <div className="flex items-center justify-center space-x-8 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>25,000+ users</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>4.9/5 rating</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>99.9% uptime</span>
          </div>
        </div>
      </div>
    </section>
  );
}
