import { Text } from "@/components/retroui/Text";

export function CloudLogoSection() {
  const partners = [
    {
      name: "Solana",
      logo: "https://cryptologos.cc/logos/solana-sol-logo.svg",
      description: "High-performance blockchain",
    },
    {
      name: "Ray<PERSON>",
      logo: "https://raydium.io/logo.svg",
      description: "Automated Market Maker",
    },
    {
      name: "OpenBook",
      logo: "https://www.openbook-solana.com/favicon.ico",
      description: "Central Limit Order Book",
    },
    {
      name: "Serum",
      logo: "https://cryptologos.cc/logos/serum-srm-logo.svg",
      description: "Decentralized Exchange",
    },
    {
      name: "Jupiter",
      logo: "https://jup.ag/favicon.ico",
      description: "Liquidity Aggregator",
    },
    {
      name: "Phantom",
      logo: "https://phantom.app/img/phantom-logo.svg",
      description: "Solana Wallet",
    },
  ];

  return (
    <section className="py-16 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <Text as="h3" className="mb-4">
            Powered by Leading Solana Protocols
          </Text>
          <Text as="p" className="text-muted-foreground max-w-2xl mx-auto">
            Built on top of the most trusted and battle-tested protocols in the
            Solana ecosystem
          </Text>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          {partners.map((partner) => (
            <div
              key={partner.name}
              className="group flex flex-col items-center justify-center p-6 bg-background border-2 border-border shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1"
            >
              <div className="w-12 h-12 mb-3 flex items-center justify-center bg-card border border-border/50 rounded-sm">
                {/* Placeholder for logo - in a real app, you'd use proper logos */}
                <div className="w-8 h-8 bg-primary/20 border border-primary/30 flex items-center justify-center text-xs font-bold text-primary">
                  {partner.name.charAt(0)}
                </div>
              </div>
              <Text
                as="h6"
                className="font-semibold text-center mb-1 group-hover:text-accent-foreground transition-colors"
              >
                {partner.name}
              </Text>
              <Text
                as="p"
                className="text-xs text-muted-foreground text-center"
              >
                {partner.description}
              </Text>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="text-center p-6 bg-background border-2 border-border shadow-sm">
            <Text
              as="h2"
              variant="brand"
              className="text-accent-foreground mb-2"
            >
              $2.5B+
            </Text>
            <Text as="p" className="text-muted-foreground">
              Total Volume Processed
            </Text>
          </div>
          <div className="text-center p-6 bg-background border-2 border-border shadow-sm">
            <Text
              as="h2"
              variant="brand"
              className="text-accent-foreground mb-2"
            >
              50K+
            </Text>
            <Text as="p" className="text-muted-foreground">
              Tokens Created
            </Text>
          </div>
          <div className="text-center p-6 bg-background border-2 border-border shadow-sm">
            <Text
              as="h2"
              variant="brand"
              className="text-accent-foreground mb-2"
            >
              25K+
            </Text>
            <Text as="p" className="text-muted-foreground">
              Active Users
            </Text>
          </div>
          <div className="text-center p-6 bg-background border-2 border-border shadow-sm">
            <Text
              as="h2"
              variant="brand"
              className="text-accent-foreground mb-2"
            >
              99.9%
            </Text>
            <Text as="p" className="text-muted-foreground">
              Uptime
            </Text>
          </div>
        </div>
      </div>
    </section>
  );
}
