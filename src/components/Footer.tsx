import Link from "next/link";
import { Text } from "@/components/retroui/Text";
import { Button } from "@/components/retroui/Button";

export function Footer() {
  const footerSections = [
    {
      title: "Platform",
      links: [
        { label: "Token Creator", href: "/tools/token-creator" },
        { label: "Liquidity Manager", href: "/tools/liquidity" },
        { label: "Market Tools", href: "/tools/markets" },
        { label: "Portfolio Tracker", href: "/tools/portfolio" },
        { label: "API Access", href: "/api" },
      ],
    },
    {
      title: "Resources",
      links: [
        { label: "Documentation", href: "/docs" },
        { label: "Tutorials", href: "/tutorials" },
        { label: "Blog", href: "/blog" },
        { label: "Changelog", href: "/changelog" },
        { label: "Status Page", href: "/status", external: true },
      ],
    },
    {
      title: "Community",
      links: [
        {
          label: "Discord",
          href: "https://discord.gg/dexnity",
          external: true,
        },
        {
          label: "Twitter",
          href: "https://twitter.com/dexnity",
          external: true,
        },
        { label: "GitHub", href: "https://github.com/dexnity", external: true },
        { label: "Telegram", href: "https://t.me/dexnity", external: true },
        {
          label: "Reddit",
          href: "https://reddit.com/r/dexnity",
          external: true,
        },
      ],
    },
    {
      title: "Company",
      links: [
        { label: "About Us", href: "/about" },
        { label: "Careers", href: "/careers" },
        { label: "Press Kit", href: "/press" },
        { label: "Contact", href: "/contact" },
        { label: "Partners", href: "/partners" },
      ],
    },
  ];

  const socialLinks = [
    { href: "https://twitter.com/dexnity", label: "Twitter" },
    { href: "https://github.com/dexnity", label: "GitHub" },
    { href: "https://discord.gg/dexnity", label: "Discord" },
    { href: "mailto:<EMAIL>", label: "Email" },
  ];

  return (
    <footer className="bg-background border-t-2 border-border">
      {/* Newsletter Section */}
      <div className="border-b-2 border-border bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <Text as="h3" className="mb-4">
                Stay Updated with dexnity.com
              </Text>
              <Text as="p" className="text-muted-foreground">
                Get the latest updates on new features, Solana ecosystem news,
                and exclusive insights delivered to your inbox.
              </Text>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="w-full px-4 py-3 bg-background border-2 border-border shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                />
              </div>
              <Button>Subscribe</Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <div className="w-8 h-8 bg-primary border-2 border-border shadow-sm flex items-center justify-center">
                <Text as="h6" className="font-bold text-primary-foreground">
                  D
                </Text>
              </div>
              <Text as="h4" className="font-bold text-foreground">
                dexnity.com
              </Text>
            </Link>
            <Text as="p" className="text-muted-foreground mb-6 leading-relaxed">
              The ultimate Solana DEX tools platform. Create, manage, and trade
              tokens with professional-grade DeFi tools built for the Solana
              ecosystem.
            </Text>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-2 bg-muted border-2 border-border shadow-sm hover:shadow-md hover:bg-primary hover:text-primary-foreground transition-all duration-200 text-sm font-medium"
                  aria-label={social.label}
                >
                  {social.label}
                </a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <Text as="h5" className="font-semibold mb-4">
                {section.title}
              </Text>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.label}>
                    {link.external ? (
                      <a
                        href={link.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-muted-foreground hover:text-primary transition-colors duration-200"
                      >
                        {link.label} ↗
                      </a>
                    ) : (
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors duration-200"
                      >
                        {link.label}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t-2 border-border bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <Text as="p" className="text-sm text-muted-foreground">
                © 2024 dexnity.com. All rights reserved.
              </Text>
              <div className="flex space-x-6">
                <Link
                  href="/privacy"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  Privacy Policy
                </Link>
                <Link
                  href="/terms"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  Terms of Service
                </Link>
                <Link
                  href="/cookies"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  Cookie Policy
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Text as="p" className="text-sm text-muted-foreground">
                Built on Solana
              </Text>
              <div className="w-6 h-6 bg-primary/20 border border-primary/30 flex items-center justify-center">
                <div className="w-3 h-3 bg-primary"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
