import { Button } from "@/components/retroui/Button";
import { Text } from "@/components/retroui/Text";
import { Badge } from "@/components/retroui/Badge";
import { ArrowRight, Zap, Shield, TrendingUp } from "lucide-react";

export function HeroSection() {
  return (
    <section className="relative bg-background py-20 lg:py-32 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,219,51,0.1)_25%,rgba(255,219,51,0.1)_50%,transparent_50%,transparent_75%,rgba(255,219,51,0.1)_75%)] bg-[length:20px_20px]"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Badge */}
          <div className="flex justify-center mb-8">
            <Badge className="bg-primary/10 text-primary border-primary/20 px-4 py-2">
              <Zap size={16} className="mr-2" />
              Powered by Solana
            </Badge>
          </div>

          {/* Main Headline */}
          <Text as="h1" className="mb-6 max-w-4xl mx-auto leading-tight">
            The Ultimate{" "}
            <Text as="span" variant="brand" className="text-accent-foreground">
              Solana DEX
            </Text>{" "}
            Tools Platform
          </Text>

          {/* Subheadline */}
          <Text
            as="p"
            className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            Create, manage, and trade tokens with ease. Access powerful DeFi
            tools for Raydium liquidity management, OpenBook market integration,
            and comprehensive token utilities—all in one platform.
          </Text>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button size="lg" className="group">
              Launch App
              <ArrowRight
                size={20}
                className="ml-2 group-hover:translate-x-1 transition-transform"
              />
            </Button>
            <Button variant="outline" size="lg">
              View Documentation
            </Button>
          </div>

          {/* Feature Highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="flex items-center justify-center space-x-3 p-4 bg-card border-2 border-border shadow-sm">
              <div className="w-10 h-10 bg-primary/10 border-2 border-primary/20 flex items-center justify-center">
                <Zap size={20} className="text-primary" />
              </div>
              <div className="text-left">
                <Text as="h6" className="font-semibold">
                  Lightning Fast
                </Text>
                <Text as="p" className="text-sm text-muted-foreground">
                  Solana-powered speed
                </Text>
              </div>
            </div>

            <div className="flex items-center justify-center space-x-3 p-4 bg-card border-2 border-border shadow-sm">
              <div className="w-10 h-10 bg-primary/10 border-2 border-primary/20 flex items-center justify-center">
                <Shield size={20} className="text-primary" />
              </div>
              <div className="text-left">
                <Text as="h6" className="font-semibold">
                  Secure & Audited
                </Text>
                <Text as="p" className="text-sm text-muted-foreground">
                  Battle-tested protocols
                </Text>
              </div>
            </div>

            <div className="flex items-center justify-center space-x-3 p-4 bg-card border-2 border-border shadow-sm">
              <div className="w-10 h-10 bg-primary/10 border-2 border-primary/20 flex items-center justify-center">
                <TrendingUp size={20} className="text-primary" />
              </div>
              <div className="text-left">
                <Text as="h6" className="font-semibold">
                  Advanced Tools
                </Text>
                <Text as="p" className="text-sm text-muted-foreground">
                  Professional-grade DeFi
                </Text>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
