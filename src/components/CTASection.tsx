import { Button } from "@/components/retroui/Button";
import { Text } from "@/components/retroui/Text";
import { Badge } from "@/components/retroui/Badge";
import { 
  ArrowRight, 
  Rocket, 
  Users, 
  Star,
  CheckCircle,
  Zap
} from "lucide-react";

export function CTASection() {
  const benefits = [
    "No setup fees or hidden costs",
    "Start with free tier, upgrade anytime",
    "24/7 customer support",
    "Battle-tested security"
  ];

  return (
    <section className="py-20 bg-background relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,219,51,0.1)_25%,rgba(255,219,51,0.1)_50%,transparent_50%,transparent_75%,rgba(255,219,51,0.1)_75%)] bg-[length:40px_40px]"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <div>
            <Badge className="mb-6 bg-primary/10 text-primary border-primary/20">
              <Rocket size={16} className="mr-2" />
              Ready to Launch
            </Badge>

            <Text as="h2" className="mb-6 leading-tight">
              Start Building on{" "}
              <span className="text-primary">Solana</span>{" "}
              Today
            </Text>

            <Text as="p" className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Join thousands of developers and traders who trust dexnity.com 
              for their Solana DeFi needs. Get started in minutes, not hours.
            </Text>

            {/* Benefits List */}
            <div className="space-y-4 mb-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-primary/10 border-2 border-primary/20 flex items-center justify-center flex-shrink-0">
                    <CheckCircle size={14} className="text-primary" />
                  </div>
                  <Text as="p" className="text-muted-foreground">
                    {benefit}
                  </Text>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <Button size="lg" className="group">
                Launch App Now
                <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                View Documentation
              </Button>
            </div>

            {/* Social Proof */}
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Users size={16} />
                <span>25,000+ users</span>
              </div>
              <div className="flex items-center space-x-2">
                <Star size={16} />
                <span>4.9/5 rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap size={16} />
                <span>99.9% uptime</span>
              </div>
            </div>
          </div>

          {/* Right Column - Feature Cards */}
          <div className="space-y-6">
            {/* Quick Start Card */}
            <div className="p-6 bg-card border-2 border-border shadow-sm">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-primary/10 border-2 border-primary/20 flex items-center justify-center">
                  <Rocket size={20} className="text-primary" />
                </div>
                <Text as="h4" className="font-semibold">
                  Quick Start
                </Text>
              </div>
              <Text as="p" className="text-muted-foreground mb-4">
                Connect your wallet and start creating tokens in under 5 minutes. 
                No complex setup required.
              </Text>
              <Button variant="outline" size="sm">
                Get Started
              </Button>
            </div>

            {/* Enterprise Card */}
            <div className="p-6 bg-primary/5 border-2 border-primary/20">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-primary/20 border-2 border-primary/30 flex items-center justify-center">
                  <Users size={20} className="text-primary" />
                </div>
                <div>
                  <Text as="h4" className="font-semibold">
                    Enterprise Solutions
                  </Text>
                  <Badge size="sm" className="mt-1">
                    Custom Pricing
                  </Badge>
                </div>
              </div>
              <Text as="p" className="text-muted-foreground mb-4">
                Need custom features, dedicated support, or white-label solutions? 
                Let's talk about your requirements.
              </Text>
              <Button size="sm">
                Contact Sales
              </Button>
            </div>

            {/* Developer Card */}
            <div className="p-6 bg-card border-2 border-border shadow-sm">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-primary/10 border-2 border-primary/20 flex items-center justify-center">
                  <Zap size={20} className="text-primary" />
                </div>
                <Text as="h4" className="font-semibold">
                  Developer API
                </Text>
              </div>
              <Text as="p" className="text-muted-foreground mb-4">
                Integrate our tools into your applications with our comprehensive 
                REST and WebSocket APIs.
              </Text>
              <Button variant="outline" size="sm">
                View API Docs
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
