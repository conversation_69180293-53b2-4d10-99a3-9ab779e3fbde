import Link from "next/link";
import { Text } from "@/components/retroui/Text";

export function SimpleFooter() {
  const footerSections = [
    {
      title: "Platform",
      links: [
        { label: "Token Creator", href: "/tools/token-creator" },
        { label: "Liquidity Manager", href: "/tools/liquidity" },
        { label: "Market Tools", href: "/tools/markets" },
        { label: "API Access", href: "/api" },
      ],
    },
    {
      title: "Resources",
      links: [
        { label: "Documentation", href: "/docs" },
        { label: "Tutorials", href: "/tutorials" },
        { label: "Blog", href: "/blog" },
        { label: "Support", href: "/support" },
      ],
    },
    {
      title: "Community",
      links: [
        { label: "Discord ↗", href: "https://discord.gg/dexnity" },
        { label: "Twitter ↗", href: "https://twitter.com/dexnity" },
        { label: "GitHub ↗", href: "https://github.com/dexnity" },
        { label: "Telegram ↗", href: "https://t.me/dexnity" },
      ],
    },
    {
      title: "Company",
      links: [
        { label: "About Us", href: "/about" },
        { label: "Careers", href: "/careers" },
        { label: "Contact", href: "/contact" },
        { label: "Partners", href: "/partners" },
      ],
    },
  ];

  return (
    <footer className="bg-background border-t-2 border-border">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <div className="w-8 h-8 bg-primary border-2 border-border shadow-sm flex items-center justify-center">
                <Text as="h6" className="font-bold text-primary-foreground">
                  D
                </Text>
              </div>
              <Text as="h4" className="font-bold text-foreground">
                dexnity.com
              </Text>
            </Link>
            <Text as="p" className="text-muted-foreground mb-6 leading-relaxed">
              The ultimate Solana DEX tools platform. Create, manage, and trade
              tokens with professional-grade DeFi tools built for the Solana ecosystem.
            </Text>
            <div className="flex flex-wrap gap-3">
              <a
                href="https://twitter.com/dexnity"
                target="_blank"
                rel="noopener noreferrer"
                className="px-3 py-2 bg-muted border-2 border-border shadow-sm hover:shadow-md hover:bg-primary hover:text-primary-foreground transition-all duration-200 text-sm font-medium"
              >
                Twitter
              </a>
              <a
                href="https://github.com/dexnity"
                target="_blank"
                rel="noopener noreferrer"
                className="px-3 py-2 bg-muted border-2 border-border shadow-sm hover:shadow-md hover:bg-primary hover:text-primary-foreground transition-all duration-200 text-sm font-medium"
              >
                GitHub
              </a>
              <a
                href="https://discord.gg/dexnity"
                target="_blank"
                rel="noopener noreferrer"
                className="px-3 py-2 bg-muted border-2 border-border shadow-sm hover:shadow-md hover:bg-primary hover:text-primary-foreground transition-all duration-200 text-sm font-medium"
              >
                Discord
              </a>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <Text as="h5" className="font-semibold mb-4">
                {section.title}
              </Text>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.label}>
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-primary transition-colors duration-200"
                      target={link.href.startsWith('http') ? '_blank' : undefined}
                      rel={link.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t-2 border-border bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <Text as="p" className="text-sm text-muted-foreground">
                © 2024 dexnity.com. All rights reserved.
              </Text>
              <div className="flex space-x-6">
                <Link
                  href="/privacy"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  Privacy Policy
                </Link>
                <Link
                  href="/terms"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  Terms of Service
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Text as="p" className="text-sm text-muted-foreground">
                Built on Solana
              </Text>
              <div className="w-6 h-6 bg-primary/20 border border-primary/30 flex items-center justify-center">
                <div className="w-3 h-3 bg-primary"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
