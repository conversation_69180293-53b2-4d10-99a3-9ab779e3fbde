import { Button } from "@/components/retroui/Button";
import { Text } from "@/components/retroui/Text";
import { Badge } from "@/components/retroui/Badge";
import { 
  Coins, 
  Settings, 
  BarChart3, 
  BookOpen, 
  Zap, 
  Shield,
  ArrowRight,
  TrendingUp,
  Wallet,
  RefreshCw
} from "lucide-react";

export function FeaturesSection() {
  const features = [
    {
      icon: Coins,
      title: "Token Creation Tools",
      description: "Create SPL tokens with custom metadata, supply controls, and advanced tokenomics. Deploy with just a few clicks.",
      badge: "Most Popular",
      badgeVariant: "default" as const,
      highlights: [
        "Custom metadata & branding",
        "Supply & mint controls",
        "Freeze authority options",
        "Batch token creation"
      ]
    },
    {
      icon: Settings,
      title: "Token Management Dashboard",
      description: "Comprehensive dashboard to manage your tokens, track performance, and execute administrative functions.",
      badge: "Pro Feature",
      badgeVariant: "outline" as const,
      highlights: [
        "Real-time analytics",
        "Holder distribution",
        "Transaction history",
        "Authority management"
      ]
    },
    {
      icon: BarChart3,
      title: "Liquidity Management",
      description: "Seamlessly manage liquidity pools on Raydium with advanced tools for LPs and market makers.",
      badge: "Advanced",
      badgeVariant: "outline" as const,
      highlights: [
        "Pool creation & management",
        "LP position tracking",
        "Yield optimization",
        "Impermanent loss protection"
      ]
    },
    {
      icon: BookOpen,
      title: "OpenBook Integration",
      description: "Direct integration with OpenBook markets for professional trading and market making activities.",
      badge: "Enterprise",
      badgeVariant: "outline" as const,
      highlights: [
        "Order book management",
        "Market making tools",
        "Advanced order types",
        "API access"
      ]
    },
    {
      icon: Zap,
      title: "Lightning Fast Execution",
      description: "Built on Solana's high-performance blockchain for instant transactions and minimal fees.",
      badge: null,
      badgeVariant: "default" as const,
      highlights: [
        "Sub-second confirmations",
        "Ultra-low fees",
        "High throughput",
        "MEV protection"
      ]
    },
    {
      icon: Shield,
      title: "Security & Audits",
      description: "Battle-tested smart contracts with multiple security audits and insurance coverage.",
      badge: "Audited",
      badgeVariant: "outline" as const,
      highlights: [
        "Multiple security audits",
        "Insurance coverage",
        "Bug bounty program",
        "24/7 monitoring"
      ]
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4">Features</Badge>
          <Text as="h2" className="mb-6">
            Everything You Need for{" "}
            <span className="text-primary">Solana DeFi</span>
          </Text>
          <Text as="p" className="text-xl text-muted-foreground max-w-3xl mx-auto">
            From token creation to advanced trading, our comprehensive suite of tools 
            empowers you to build, manage, and scale your DeFi projects on Solana.
          </Text>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group p-8 bg-card border-2 border-border shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1"
            >
              {/* Icon & Badge */}
              <div className="flex items-start justify-between mb-6">
                <div className="w-12 h-12 bg-primary/10 border-2 border-primary/20 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <feature.icon size={24} className="text-primary" />
                </div>
                {feature.badge && (
                  <Badge variant={feature.badgeVariant} size="sm">
                    {feature.badge}
                  </Badge>
                )}
              </div>

              {/* Content */}
              <Text as="h4" className="mb-3 group-hover:text-primary transition-colors">
                {feature.title}
              </Text>
              <Text as="p" className="text-muted-foreground mb-6 leading-relaxed">
                {feature.description}
              </Text>

              {/* Highlights */}
              <ul className="space-y-2 mb-6">
                {feature.highlights.map((highlight, idx) => (
                  <li key={idx} className="flex items-center text-sm text-muted-foreground">
                    <div className="w-1.5 h-1.5 bg-primary mr-3 flex-shrink-0"></div>
                    {highlight}
                  </li>
                ))}
              </ul>

              {/* CTA */}
              <Button variant="outline" size="sm" className="group/btn">
                Learn More
                <ArrowRight size={16} className="ml-2 group-hover/btn:translate-x-1 transition-transform" />
              </Button>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center p-8 bg-primary/5 border-2 border-primary/20">
          <Text as="h3" className="mb-4">
            Ready to Get Started?
          </Text>
          <Text as="p" className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Join thousands of developers and traders who trust dexnity.com for their Solana DeFi needs.
          </Text>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg">
              Launch App Now
              <ArrowRight size={20} className="ml-2" />
            </Button>
            <Button variant="outline" size="lg">
              Schedule Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
